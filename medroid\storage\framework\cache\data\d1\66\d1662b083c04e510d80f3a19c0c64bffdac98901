1749639812a:40:{s:11:"total_users";i:18;s:20:"monthly_active_users";i:16;s:18:"daily_active_users";i:1;s:16:"user_growth_rate";d:38.5;s:13:"patient_count";i:11;s:14:"provider_count";i:5;s:11:"admin_count";i:2;s:14:"verified_users";i:18;s:17:"verification_rate";d:100;s:18:"total_appointments";i:20;s:20:"monthly_appointments";i:19;s:18:"daily_appointments";i:1;s:27:"appointment_completion_rate";d:35;s:29:"appointment_cancellation_rate";d:20;s:22:"completed_appointments";i:7;s:22:"cancelled_appointments";i:4;s:14:"total_consults";i:16;s:16:"monthly_consults";i:16;s:14:"daily_consults";i:0;s:28:"consult_to_appointment_ratio";d:80;s:20:"repeat_consult_users";i:6;s:24:"appointments_by_provider";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:5:{i:0;a:3:{s:11:"provider_id";i:4;s:13:"provider_name";s:19:"Dr. Test Provider 4";s:5:"count";i:5;}i:1;a:3:{s:11:"provider_id";i:2;s:13:"provider_name";s:19:"Dr. Test Provider 2";s:5:"count";i:4;}i:2;a:3:{s:11:"provider_id";i:3;s:13:"provider_name";s:19:"Dr. Test Provider 3";s:5:"count";i:4;}i:3;a:3:{s:11:"provider_id";i:5;s:13:"provider_name";s:19:"Dr. Test Provider 5";s:5:"count";i:4;}i:4;a:3:{s:11:"provider_id";i:1;s:13:"provider_name";s:19:"Dr. Test Provider 1";s:5:"count";i:3;}}s:28:" * escapeWhenCastingToString";b:0;}s:14:"user_locations";a:0:{}s:19:"diagnostic_accuracy";i:0;s:25:"diagnostic_feedback_count";i:0;s:22:"top_accurate_diagnoses";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:24:"top_inaccurate_diagnoses";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:13:"total_revenue";d:0;s:15:"monthly_revenue";d:0;s:25:"average_appointment_value";i:0;s:19:"revenue_by_provider";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:13:"total_clinics";i:1;s:14:"active_clinics";i:1;s:26:"clinics_accepting_patients";i:1;s:20:"telemedicine_clinics";i:1;s:22:"clinic_activation_rate";d:100;s:26:"telemedicine_adoption_rate";d:100;s:16:"clinics_by_state";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:5:"state";s:2:"CA";s:5:"count";i:1;}s:11:" * original";a:2:{s:5:"state";s:2:"CA";s:5:"count";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:21:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:5:"email";i:3;s:5:"phone";i:4;s:7:"website";i:5;s:7:"address";i:6;s:4:"city";i:7;s:5:"state";i:8;s:11:"postal_code";i:9;s:7:"country";i:10;s:15:"operating_hours";i:11;s:16:"services_offered";i:12;s:14:"license_number";i:13;s:6:"tax_id";i:14;s:9:"is_active";i:15;s:20:"accepts_new_patients";i:16;s:20:"telemedicine_enabled";i:17;s:18:"insurance_accepted";i:18;s:4:"logo";i:19;s:13:"primary_color";i:20;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:24:"top_clinics_by_providers";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:25:{s:2:"id";i:1;s:4:"name";s:25:"Medroid Healthcare Center";s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:14:"(*************";s:7:"website";N;s:7:"address";s:20:"123 Healthcare Drive";s:4:"city";s:12:"Medical City";s:5:"state";s:2:"CA";s:11:"postal_code";s:5:"90210";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-07 02:53:10";s:10:"updated_at";s:19:"2025-06-07 02:53:10";s:15:"providers_count";i:0;}s:11:" * original";a:25:{s:2:"id";i:1;s:4:"name";s:25:"Medroid Healthcare Center";s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:14:"(*************";s:7:"website";N;s:7:"address";s:20:"123 Healthcare Drive";s:4:"city";s:12:"Medical City";s:5:"state";s:2:"CA";s:11:"postal_code";s:5:"90210";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-07 02:53:10";s:10:"updated_at";s:19:"2025-06-07 02:53:10";s:15:"providers_count";i:0;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:21:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:5:"email";i:3;s:5:"phone";i:4;s:7:"website";i:5;s:7:"address";i:6;s:4:"city";i:7;s:5:"state";i:8;s:11:"postal_code";i:9;s:7:"country";i:10;s:15:"operating_hours";i:11;s:16:"services_offered";i:12;s:14:"license_number";i:13;s:6:"tax_id";i:14;s:9:"is_active";i:15;s:20:"accepts_new_patients";i:16;s:20:"telemedicine_enabled";i:17;s:18:"insurance_accepted";i:18;s:4:"logo";i:19;s:13:"primary_color";i:20;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:23:"top_clinics_by_patients";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:25:{s:2:"id";i:1;s:4:"name";s:25:"Medroid Healthcare Center";s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:14:"(*************";s:7:"website";N;s:7:"address";s:20:"123 Healthcare Drive";s:4:"city";s:12:"Medical City";s:5:"state";s:2:"CA";s:11:"postal_code";s:5:"90210";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-07 02:53:10";s:10:"updated_at";s:19:"2025-06-07 02:53:10";s:14:"patients_count";i:1;}s:11:" * original";a:25:{s:2:"id";i:1;s:4:"name";s:25:"Medroid Healthcare Center";s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:14:"(*************";s:7:"website";N;s:7:"address";s:20:"123 Healthcare Drive";s:4:"city";s:12:"Medical City";s:5:"state";s:2:"CA";s:11:"postal_code";s:5:"90210";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-07 02:53:10";s:10:"updated_at";s:19:"2025-06-07 02:53:10";s:14:"patients_count";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:21:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:5:"email";i:3;s:5:"phone";i:4;s:7:"website";i:5;s:7:"address";i:6;s:4:"city";i:7;s:5:"state";i:8;s:11:"postal_code";i:9;s:7:"country";i:10;s:15:"operating_hours";i:11;s:16:"services_offered";i:12;s:14:"license_number";i:13;s:6:"tax_id";i:14;s:9:"is_active";i:15;s:20:"accepts_new_patients";i:16;s:20:"telemedicine_enabled";i:17;s:18:"insurance_accepted";i:18;s:4:"logo";i:19;s:13:"primary_color";i:20;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}